"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"relative z-50 flex justify-between items-center px-6 py-6 lg:px-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-orbitron text-2xl font-bold text-white\",\n                        children: \"ReferralAI\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: [\n                            'Platform',\n                            'Features',\n                            'Pricing',\n                            'Contact'\n                        ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\".concat(item.toLowerCase()),\n                                className: \"text-slate-300 hover:text-white transition-colors duration-200 font-medium\",\n                                children: item\n                            }, item, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-full font-semibold transition-colors duration-200\",\n                        children: \"Get Started\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 flex flex-col items-center justify-center min-h-[85vh] px-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            scale: 0.5,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.8,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 lg:w-24 lg:h-24 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-10 h-10 lg:w-12 lg:h-12 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h1, {\n                        initial: {\n                            y: 30,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 font-orbitron leading-tight\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-shimmer\",\n                                children: \"The Future of\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-blue-400 via-purple-500 to-blue-600 bg-clip-text text-transparent\",\n                                children: \"Referrals\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"text-lg lg:text-xl text-slate-300 mb-10 max-w-3xl leading-relaxed text-balance\",\n                        children: \"Transform your business with AI-powered referrals. Where anyone can earn by sharing, businesses pay only for results, and customers get better deals through trusted connections.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            y: 20,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.6\n                        },\n                        className: \"flex flex-col sm:flex-row gap-4 mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"btn-primary px-8 py-3 lg:px-10 lg:py-4 rounded-full font-semibold text-white flex items-center justify-center gap-3 text-base lg:text-lg\",\n                                children: [\n                                    \"Start Earning Now\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"btn-secondary px-8 py-3 lg:px-10 lg:py-4 rounded-full font-semibold text-slate-300 text-base lg:text-lg\",\n                                children: \"Watch Demo\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            y: 50,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 1.2\n                        },\n                        className: \"mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl\",\n                        children: [\n                            {\n                                number: '10M+',\n                                label: 'Referrals Processed',\n                                icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                            },\n                            {\n                                number: '500%',\n                                label: 'Average ROI Increase',\n                                icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                            },\n                            {\n                                number: '99.9%',\n                                label: 'Platform Uptime',\n                                icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                initial: {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    y: 0,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1.4 + index * 0.2\n                                },\n                                whileHover: {\n                                    y: -10,\n                                    scale: 1.05\n                                },\n                                className: \"text-center p-6 rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700/50 glow-silver\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"w-8 h-8 mx-auto mb-4 text-silver\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-shimmer font-orbitron mb-2\",\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-slate-400\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-20 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                y: 50,\n                                opacity: 0\n                            },\n                            whileInView: {\n                                y: 0,\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-5xl lg:text-6xl font-bold mb-6 font-orbitron\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-shimmer\",\n                                            children: \"Revolutionary\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-slate-300 max-w-3xl mx-auto\",\n                                    children: \"Experience the next generation of referral marketing with AI-powered insights and seamless automation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                    title: 'AI-Powered Matching',\n                                    description: 'Our advanced AI connects the right referrers with the perfect opportunities, maximizing success rates.',\n                                    gradient: 'from-yellow-400/20 to-orange-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                    title: 'Community-Driven Growth',\n                                    description: 'Build a network of trusted referrers who become your most powerful marketing channel.',\n                                    gradient: 'from-blue-400/20 to-purple-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    title: 'Performance Analytics',\n                                    description: 'Real-time insights and predictive analytics help optimize your referral campaigns for maximum ROI.',\n                                    gradient: 'from-green-400/20 to-emerald-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                    title: 'Secure & Transparent',\n                                    description: 'Blockchain-verified transactions ensure complete transparency and trust in every referral.',\n                                    gradient: 'from-purple-400/20 to-pink-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                                    title: 'Gamified Experience',\n                                    description: 'Engaging reward systems and leaderboards keep referrers motivated and active.',\n                                    gradient: 'from-pink-400/20 to-red-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                    title: 'Premium Support',\n                                    description: '24/7 dedicated support with AI-assisted troubleshooting and human expertise.',\n                                    gradient: 'from-indigo-400/20 to-blue-500/20'\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        y: 50,\n                                        opacity: 0\n                                    },\n                                    whileInView: {\n                                        y: 0,\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1,\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    whileHover: {\n                                        y: -10,\n                                        scale: 1.02,\n                                        boxShadow: '0 20px 40px rgba(192, 192, 192, 0.2)'\n                                    },\n                                    className: \"p-8 rounded-3xl bg-gradient-to-br \".concat(feature.gradient, \" backdrop-blur-sm border border-slate-700/50 hover:border-silver/30 transition-all duration-500 group\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"w-12 h-12 text-silver group-hover:scale-110 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold mb-4 text-white group-hover:text-shimmer transition-all duration-300\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-300 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.title, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-20 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        scale: 0.9,\n                        opacity: 0\n                    },\n                    whileInView: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 1\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-12 rounded-3xl bg-gradient-to-br from-slate-800/80 to-slate-900/80 backdrop-blur-sm border border-slate-700/50 glow-silver-strong\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                animate: {\n                                    rotate: 360\n                                },\n                                transition: {\n                                    duration: 30,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                className: \"absolute top-4 right-4 w-16 h-16 morph-shape bg-gradient-to-br from-silver/30 to-white/30\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl lg:text-5xl font-bold mb-6 font-orbitron\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-shimmer\",\n                                        children: \"Ready to Transform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent\",\n                                        children: \"Your Business?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-slate-300 mb-8 max-w-2xl mx-auto\",\n                                children: \"Join thousands of businesses already leveraging the power of AI-driven referrals. Start your journey today and watch your growth accelerate.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: '0 0 50px rgba(192, 192, 192, 0.8)',\n                                    y: -5\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-gradient-to-r from-silver to-white text-black px-12 py-5 rounded-full font-bold text-xl flex items-center gap-4 mx-auto pulse-glow\",\n                                children: [\n                                    \"Launch Your Platform\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});
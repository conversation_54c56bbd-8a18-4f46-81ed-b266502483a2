"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Sparkles,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Home.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen hero-bg\",\n        children: [\n            [\n                ...Array(8)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"particle\",\n                    style: {\n                        left: \"\".concat(Math.random() * 100, \"%\"),\n                        top: \"\".concat(Math.random() * 100, \"%\"),\n                        animationDelay: \"\".concat(Math.random() * 8, \"s\")\n                    }\n                }, i, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.nav, {\n                initial: {\n                    y: -50,\n                    opacity: 0\n                },\n                animate: {\n                    y: 0,\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8,\n                    ease: \"easeOut\"\n                },\n                className: \"relative z-20 flex justify-between items-center px-6 py-4 lg:px-8 lg:py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-orbitron text-xl lg:text-2xl font-bold text-shimmer\",\n                        children: \"ReferralAI\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-6\",\n                        children: [\n                            'Platform',\n                            'Features',\n                            'Pricing',\n                            'Contact'\n                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                href: \"#\".concat(item.toLowerCase()),\n                                initial: {\n                                    y: -20,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    y: 0,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: index * 0.1 + 0.3\n                                },\n                                className: \"text-slate-300 hover:text-slate-100 smooth-animation font-medium text-sm lg:text-base\",\n                                children: item\n                            }, item, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"btn-primary px-4 py-2 lg:px-6 lg:py-2 rounded-full font-semibold text-white text-sm lg:text-base\",\n                        children: \"Get Started\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 flex flex-col items-center justify-center min-h-[90vh] px-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            scale: 0.8,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1.2,\n                            ease: \"easeOut\"\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            animate: {\n                                rotate: 360\n                            },\n                            transition: {\n                                duration: 20,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                            },\n                            className: \"w-32 h-32 mx-auto mb-8 morph-shape bg-gradient-to-br from-silver via-slate-300 to-silver glow-silver-strong\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                        initial: {\n                            y: 50,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 0.3\n                        },\n                        className: \"text-6xl lg:text-8xl font-bold mb-6 font-orbitron\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-shimmer\",\n                                children: \"The Future of\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                animate: {\n                                    backgroundPosition: [\n                                        '0% 50%',\n                                        '100% 50%',\n                                        '0% 50%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3,\n                                    repeat: Infinity\n                                },\n                                className: \"bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent bg-[length:200%_100%]\",\n                                children: \"Referrals\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                        initial: {\n                            y: 30,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 0.6\n                        },\n                        className: \"text-xl lg:text-2xl text-slate-300 mb-12 max-w-4xl leading-relaxed\",\n                        children: \"Transform your business with AI-powered referrals. Where anyone can earn by sharing, businesses pay only for results, and customers get better deals through trusted connections.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            y: 30,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 0.9\n                        },\n                        className: \"flex flex-col sm:flex-row gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: '0 0 40px rgba(192, 192, 192, 0.6)',\n                                    y: -5\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-gradient-to-r from-silver to-white text-black px-8 py-4 rounded-full font-bold text-lg flex items-center gap-3 pulse-glow\",\n                                children: [\n                                    \"Start Earning Now\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    borderColor: '#c0c0c0',\n                                    color: '#c0c0c0'\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"border-2 border-slate-600 text-slate-300 px-8 py-4 rounded-full font-bold text-lg hover:bg-slate-800/50 transition-all duration-300\",\n                                children: \"Watch Demo\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            y: 50,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 1.2\n                        },\n                        className: \"mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl\",\n                        children: [\n                            {\n                                number: '10M+',\n                                label: 'Referrals Processed',\n                                icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                            },\n                            {\n                                number: '500%',\n                                label: 'Average ROI Increase',\n                                icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                            },\n                            {\n                                number: '99.9%',\n                                label: 'Platform Uptime',\n                                icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    y: 0,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1.4 + index * 0.2\n                                },\n                                whileHover: {\n                                    y: -10,\n                                    scale: 1.05\n                                },\n                                className: \"text-center p-6 rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700/50 glow-silver\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"w-8 h-8 mx-auto mb-4 text-silver\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-shimmer font-orbitron mb-2\",\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-slate-400\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-20 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                y: 50,\n                                opacity: 0\n                            },\n                            whileInView: {\n                                y: 0,\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-5xl lg:text-6xl font-bold mb-6 font-orbitron\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-shimmer\",\n                                            children: \"Revolutionary\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-slate-300 max-w-3xl mx-auto\",\n                                    children: \"Experience the next generation of referral marketing with AI-powered insights and seamless automation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                    title: 'AI-Powered Matching',\n                                    description: 'Our advanced AI connects the right referrers with the perfect opportunities, maximizing success rates.',\n                                    gradient: 'from-yellow-400/20 to-orange-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                    title: 'Community-Driven Growth',\n                                    description: 'Build a network of trusted referrers who become your most powerful marketing channel.',\n                                    gradient: 'from-blue-400/20 to-purple-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    title: 'Performance Analytics',\n                                    description: 'Real-time insights and predictive analytics help optimize your referral campaigns for maximum ROI.',\n                                    gradient: 'from-green-400/20 to-emerald-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                    title: 'Secure & Transparent',\n                                    description: 'Blockchain-verified transactions ensure complete transparency and trust in every referral.',\n                                    gradient: 'from-purple-400/20 to-pink-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                    title: 'Gamified Experience',\n                                    description: 'Engaging reward systems and leaderboards keep referrers motivated and active.',\n                                    gradient: 'from-pink-400/20 to-red-500/20'\n                                },\n                                {\n                                    icon: _barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    title: 'Premium Support',\n                                    description: '24/7 dedicated support with AI-assisted troubleshooting and human expertise.',\n                                    gradient: 'from-indigo-400/20 to-blue-500/20'\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        y: 50,\n                                        opacity: 0\n                                    },\n                                    whileInView: {\n                                        y: 0,\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1,\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    whileHover: {\n                                        y: -10,\n                                        scale: 1.02,\n                                        boxShadow: '0 20px 40px rgba(192, 192, 192, 0.2)'\n                                    },\n                                    className: \"p-8 rounded-3xl bg-gradient-to-br \".concat(feature.gradient, \" backdrop-blur-sm border border-slate-700/50 hover:border-silver/30 transition-all duration-500 group\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"w-12 h-12 text-silver group-hover:scale-110 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold mb-4 text-white group-hover:text-shimmer transition-all duration-300\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-300 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.title, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-20 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        scale: 0.9,\n                        opacity: 0\n                    },\n                    whileInView: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 1\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-12 rounded-3xl bg-gradient-to-br from-slate-800/80 to-slate-900/80 backdrop-blur-sm border border-slate-700/50 glow-silver-strong\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                animate: {\n                                    rotate: 360\n                                },\n                                transition: {\n                                    duration: 30,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                className: \"absolute top-4 right-4 w-16 h-16 morph-shape bg-gradient-to-br from-silver/30 to-white/30\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl lg:text-5xl font-bold mb-6 font-orbitron\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-shimmer\",\n                                        children: \"Ready to Transform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent\",\n                                        children: \"Your Business?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-slate-300 mb-8 max-w-2xl mx-auto\",\n                                children: \"Join thousands of businesses already leveraging the power of AI-driven referrals. Start your journey today and watch your growth accelerate.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: '0 0 50px rgba(192, 192, 192, 0.8)',\n                                    y: -5\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-gradient-to-r from-silver to-white text-black px-12 py-5 rounded-full font-bold text-xl flex items-center gap-4 mx-auto pulse-glow\",\n                                children: [\n                                    \"Launch Your Platform\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Sparkles_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/joe-porter-referral/referral-platform/src/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"LrrVfNW3d1raFE0BNzCTILYmIfo=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});
import { <PERSON><PERSON>ight, Zap, Users, TrendingUp, Star, Shield, Sparkles, DollarSign, Target, Award } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-black/80 backdrop-blur-md border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="font-orbitron text-2xl font-bold text-white">
              ReferralAI
            </div>

            <div className="hidden md:flex items-center space-x-8">
              {['Platform', 'Features', 'Pricing', 'Contact'].map((item) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase()}`}
                  className="text-gray-300 hover:text-white transition-colors duration-200 font-medium"
                >
                  {item}
                </a>
              ))}
            </div>

            <button className="bg-gradient-to-r from-gray-500 to-gray-300 hover:from-gray-400 hover:to-gray-200 text-black px-6 py-2 rounded-full font-semibold transition-all duration-200">
              Get Started
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center">
            {/* Logo Icon */}
            <div className="mb-8">
              <div className="w-20 h-20 mx-auto rounded-xl bg-gradient-to-br from-gray-500 to-gray-300 flex items-center justify-center shadow-xl">
                <Sparkles className="w-10 h-10 text-black" />
              </div>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron leading-tight">
              <span className="text-white">The Future of</span>
              <br />
              <span className="bg-gradient-to-r from-gray-400 via-gray-200 to-gray-400 bg-clip-text text-transparent">
                AI-Powered Referrals
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-lg lg:text-xl text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed">
              Transform your business with the world's most advanced referral platform. Where anyone can earn by sharing,
              businesses pay only for confirmed results, and customers get better deals through trusted connections.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <button className="bg-gradient-to-r from-gray-500 to-gray-300 hover:from-gray-400 hover:to-gray-200 text-black px-8 py-3 rounded-full font-semibold flex items-center justify-center gap-2 transition-all duration-200">
                Start Earning Now
                <ArrowRight className="w-4 h-4" />
              </button>

              <button className="border-2 border-gray-600 hover:border-gray-400 text-gray-300 hover:text-white px-8 py-3 rounded-full font-semibold transition-all duration-200">
                Watch Demo
              </button>
            </div>

            {/* Stats Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {[
                { number: '10M+', label: 'Referrals Processed', icon: Users },
                { number: '500%', label: 'Average ROI Increase', icon: TrendingUp },
                { number: '99.9%', label: 'Platform Uptime', icon: Shield },
              ].map((stat) => (
                <div
                  key={stat.label}
                  className="text-center p-6 rounded-xl bg-gray-900/30 border border-gray-800 hover:bg-gray-900/50 transition-all duration-200"
                >
                  <stat.icon className="w-6 h-6 mx-auto mb-3 text-gray-400" />
                  <div className="text-2xl font-bold text-white font-orbitron mb-1">{stat.number}</div>
                  <div className="text-sm text-gray-400">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-900/20">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 font-orbitron text-white">
              Revolutionary Features
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Experience the next generation of referral marketing with AI-powered insights
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                icon: Zap,
                title: 'AI-Powered Matching',
                description: 'Advanced AI connects the right referrers with perfect opportunities.',
              },
              {
                icon: Users,
                title: 'Community Growth',
                description: 'Build a network of trusted referrers as your marketing channel.',
              },
              {
                icon: TrendingUp,
                title: 'Performance Analytics',
                description: 'Real-time insights help optimize campaigns for maximum ROI.',
              },
              {
                icon: Shield,
                title: 'Secure & Transparent',
                description: 'Blockchain-verified transactions ensure complete transparency.',
              },
              {
                icon: DollarSign,
                title: 'Instant Payouts',
                description: 'Get paid instantly when referrals convert. No delays.',
              },
              {
                icon: Star,
                title: 'Premium Support',
                description: '24/7 dedicated support with expert assistance.',
              }
            ].map((feature) => (
              <div
                key={feature.title}
                className="p-6 rounded-xl bg-gray-900/30 border border-gray-800 hover:bg-gray-900/50 transition-all duration-200"
              >
                <div className="mb-4">
                  <feature.icon className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold mb-2 text-white">
                  {feature.title}
                </h3>
                <p className="text-sm text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 font-orbitron text-white">
              How It Works
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Three simple steps to transform your business growth
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                icon: Target,
                title: 'Create Your Offer',
                description: 'Set up your referral campaign with AI-optimized rewards.',
              },
              {
                step: '02',
                icon: Users,
                title: 'Share & Earn',
                description: 'Referrers share offers and earn commissions on conversions.',
              },
              {
                step: '03',
                icon: Award,
                title: 'Track Results',
                description: 'Monitor performance and scale successful campaigns.',
              }
            ].map((step) => (
              <div key={step.title} className="text-center">
                <div className="relative mb-6">
                  <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-gray-500 to-gray-300 flex items-center justify-center">
                    <step.icon className="w-8 h-8 text-black" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-gray-800 border-2 border-gray-600 flex items-center justify-center">
                    <span className="text-xs font-bold text-gray-300">{step.step}</span>
                  </div>
                </div>
                <h3 className="text-lg font-semibold mb-2 text-white">{step.title}</h3>
                <p className="text-sm text-gray-400 leading-relaxed">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-900/20">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center p-8 rounded-xl bg-gray-900/30 border border-gray-800">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 font-orbitron text-white">
              Ready to Transform Your Business?
            </h2>

            <p className="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of businesses leveraging AI-driven referrals.
              Start your journey today.
            </p>

            <button className="bg-gradient-to-r from-gray-500 to-gray-300 hover:from-gray-400 hover:to-gray-200 text-black px-8 py-3 rounded-full font-semibold flex items-center gap-2 mx-auto transition-all duration-200">
              Launch Your Platform
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t border-gray-800">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center">
            <div className="font-orbitron text-lg font-bold text-white mb-2">
              ReferralAI
            </div>
            <p className="text-gray-400 mb-4 text-sm">The future of referral marketing is here.</p>
            <div className="flex justify-center space-x-6 text-sm">
              {['Privacy', 'Terms', 'Support', 'API'].map((item) => (
                <a key={item} href="#" className="text-gray-400 hover:text-gray-300 transition-colors duration-200">
                  {item}
                </a>
              ))}
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

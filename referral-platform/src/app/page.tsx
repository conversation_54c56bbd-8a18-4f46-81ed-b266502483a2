'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Zap, Users, TrendingUp, Star, Shield, Sparkles } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function Home() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen animated-bg relative overflow-hidden">
      {/* Floating Particles */}
      {[...Array(20)].map((_, i) => (
        <div
          key={i}
          className="particle"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            width: `${Math.random() * 4 + 2}px`,
            height: `${Math.random() * 4 + 2}px`,
            animationDelay: `${Math.random() * 6}s`,
          }}
        />
      ))}

      {/* Mouse follower glow */}
      <div
        className="fixed pointer-events-none z-0 w-96 h-96 rounded-full opacity-20"
        style={{
          background: 'radial-gradient(circle, rgba(192, 192, 192, 0.3) 0%, transparent 70%)',
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
          transition: 'all 0.1s ease-out',
        }}
      />

      {/* Navigation */}
      <motion.nav
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 1, ease: "easeOut" }}
        className="relative z-10 flex justify-between items-center p-6 lg:p-8"
      >
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="font-orbitron text-2xl font-bold text-shimmer"
        >
          ReferralAI
        </motion.div>

        <div className="hidden md:flex space-x-8">
          {['Platform', 'Features', 'Pricing', 'Contact'].map((item, index) => (
            <motion.a
              key={item}
              href={`#${item.toLowerCase()}`}
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: index * 0.1 + 0.5 }}
              whileHover={{ y: -2, color: '#c0c0c0' }}
              className="text-slate-300 hover:text-silver transition-all duration-300 font-medium"
            >
              {item}
            </motion.a>
          ))}
        </div>

        <motion.button
          whileHover={{ scale: 1.05, boxShadow: '0 0 30px rgba(192, 192, 192, 0.5)' }}
          whileTap={{ scale: 0.95 }}
          className="bg-gradient-to-r from-silver to-slate-300 text-black px-6 py-2 rounded-full font-semibold glow-silver"
        >
          Get Started
        </motion.button>
      </motion.nav>

      {/* Hero Section */}
      <section className="relative z-10 flex flex-col items-center justify-center min-h-[90vh] px-6 text-center">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1.2, ease: "easeOut" }}
          className="mb-8"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="w-32 h-32 mx-auto mb-8 morph-shape bg-gradient-to-br from-silver via-slate-300 to-silver glow-silver-strong"
          />
        </motion.div>

        <motion.h1
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.3 }}
          className="text-6xl lg:text-8xl font-bold mb-6 font-orbitron"
        >
          <span className="text-shimmer">The Future of</span>
          <br />
          <motion.span
            animate={{
              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
            }}
            transition={{ duration: 3, repeat: Infinity }}
            className="bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent bg-[length:200%_100%]"
          >
            Referrals
          </motion.span>
        </motion.h1>

        <motion.p
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.6 }}
          className="text-xl lg:text-2xl text-slate-300 mb-12 max-w-4xl leading-relaxed"
        >
          Transform your business with AI-powered referrals. Where anyone can earn by sharing,
          businesses pay only for results, and customers get better deals through trusted connections.
        </motion.p>

        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.9 }}
          className="flex flex-col sm:flex-row gap-6"
        >
          <motion.button
            whileHover={{
              scale: 1.05,
              boxShadow: '0 0 40px rgba(192, 192, 192, 0.6)',
              y: -5
            }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-silver to-white text-black px-8 py-4 rounded-full font-bold text-lg flex items-center gap-3 pulse-glow"
          >
            Start Earning Now
            <ArrowRight className="w-5 h-5" />
          </motion.button>

          <motion.button
            whileHover={{
              scale: 1.05,
              borderColor: '#c0c0c0',
              color: '#c0c0c0'
            }}
            whileTap={{ scale: 0.95 }}
            className="border-2 border-slate-600 text-slate-300 px-8 py-4 rounded-full font-bold text-lg hover:bg-slate-800/50 transition-all duration-300"
          >
            Watch Demo
          </motion.button>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 1.2 }}
          className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl"
        >
          {[
            { number: '10M+', label: 'Referrals Processed', icon: Users },
            { number: '500%', label: 'Average ROI Increase', icon: TrendingUp },
            { number: '99.9%', label: 'Platform Uptime', icon: Shield },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.4 + index * 0.2 }}
              whileHover={{ y: -10, scale: 1.05 }}
              className="text-center p-6 rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700/50 glow-silver"
            >
              <stat.icon className="w-8 h-8 mx-auto mb-4 text-silver" />
              <div className="text-3xl font-bold text-shimmer font-orbitron mb-2">{stat.number}</div>
              <div className="text-slate-400">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="relative z-10 py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl lg:text-6xl font-bold mb-6 font-orbitron">
              <span className="text-shimmer">Revolutionary</span>
              <br />
              <span className="bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent">
                Features
              </span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Experience the next generation of referral marketing with AI-powered insights and seamless automation
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: 'AI-Powered Matching',
                description: 'Our advanced AI connects the right referrers with the perfect opportunities, maximizing success rates.',
                gradient: 'from-yellow-400/20 to-orange-500/20'
              },
              {
                icon: Users,
                title: 'Community-Driven Growth',
                description: 'Build a network of trusted referrers who become your most powerful marketing channel.',
                gradient: 'from-blue-400/20 to-purple-500/20'
              },
              {
                icon: TrendingUp,
                title: 'Performance Analytics',
                description: 'Real-time insights and predictive analytics help optimize your referral campaigns for maximum ROI.',
                gradient: 'from-green-400/20 to-emerald-500/20'
              },
              {
                icon: Shield,
                title: 'Secure & Transparent',
                description: 'Blockchain-verified transactions ensure complete transparency and trust in every referral.',
                gradient: 'from-purple-400/20 to-pink-500/20'
              },
              {
                icon: Sparkles,
                title: 'Gamified Experience',
                description: 'Engaging reward systems and leaderboards keep referrers motivated and active.',
                gradient: 'from-pink-400/20 to-red-500/20'
              },
              {
                icon: Star,
                title: 'Premium Support',
                description: '24/7 dedicated support with AI-assisted troubleshooting and human expertise.',
                gradient: 'from-indigo-400/20 to-blue-500/20'
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ delay: index * 0.1, duration: 0.8 }}
                viewport={{ once: true }}
                whileHover={{
                  y: -10,
                  scale: 1.02,
                  boxShadow: '0 20px 40px rgba(192, 192, 192, 0.2)'
                }}
                className={`p-8 rounded-3xl bg-gradient-to-br ${feature.gradient} backdrop-blur-sm border border-slate-700/50 hover:border-silver/30 transition-all duration-500 group`}
              >
                <div className="mb-6">
                  <feature.icon className="w-12 h-12 text-silver group-hover:scale-110 transition-transform duration-300" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-shimmer transition-all duration-300">
                  {feature.title}
                </h3>
                <p className="text-slate-300 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 px-6">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto text-center"
        >
          <div className="relative p-12 rounded-3xl bg-gradient-to-br from-slate-800/80 to-slate-900/80 backdrop-blur-sm border border-slate-700/50 glow-silver-strong">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
              className="absolute top-4 right-4 w-16 h-16 morph-shape bg-gradient-to-br from-silver/30 to-white/30"
            />

            <h2 className="text-4xl lg:text-5xl font-bold mb-6 font-orbitron">
              <span className="text-shimmer">Ready to Transform</span>
              <br />
              <span className="bg-gradient-to-r from-silver via-white to-silver bg-clip-text text-transparent">
                Your Business?
              </span>
            </h2>

            <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
              Join thousands of businesses already leveraging the power of AI-driven referrals.
              Start your journey today and watch your growth accelerate.
            </p>

            <motion.button
              whileHover={{
                scale: 1.05,
                boxShadow: '0 0 50px rgba(192, 192, 192, 0.8)',
                y: -5
              }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-silver to-white text-black px-12 py-5 rounded-full font-bold text-xl flex items-center gap-4 mx-auto pulse-glow"
            >
              Launch Your Platform
              <ArrowRight className="w-6 h-6" />
            </motion.button>
          </div>
        </motion.div>
      </section>
    </div>
  );
}

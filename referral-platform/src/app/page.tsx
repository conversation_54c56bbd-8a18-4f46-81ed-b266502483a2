import { <PERSON><PERSON><PERSON>, Zap, Users, TrendingUp, Star, Shield, Sparkles, DollarSign, Target, Award } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <nav className="flex justify-between items-center px-8 py-6 bg-black/90 backdrop-blur-sm border-b border-gray-800">
        <div className="font-orbitron text-2xl font-bold bg-gradient-to-r from-gray-400 to-white bg-clip-text text-transparent">
          ReferralAI
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          {['Platform', 'Features', 'Pricing', 'Contact'].map((item) => (
            <a
              key={item}
              href={`#${item.toLowerCase()}`}
              className="text-gray-300 hover:text-gray-100 transition-all duration-300 font-medium relative group"
            >
              {item}
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gray-400 transition-all duration-300 group-hover:w-full"></span>
            </a>
          ))}
        </div>

        <button className="bg-gradient-to-r from-gray-600 to-gray-400 hover:from-gray-400 hover:to-white text-black px-6 py-2 rounded-full font-semibold transition-all duration-300 shadow-lg hover:shadow-gray-400/20">
          Get Started
        </button>
      </nav>

      {/* Hero Section */}
      <section className="relative px-8 py-20 lg:py-32 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900/20 via-black to-gray-900/20"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gray-400/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gray-500/5 rounded-full blur-3xl"></div>
        
        <div className="relative max-w-6xl mx-auto text-center">
          {/* Logo Icon */}
          <div className="mb-12">
            <div className="w-24 h-24 mx-auto rounded-2xl bg-gradient-to-br from-gray-600 to-gray-400 flex items-center justify-center shadow-2xl shadow-gray-400/20">
              <Sparkles className="w-12 h-12 text-black" />
            </div>
          </div>

          {/* Main Heading */}
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 font-orbitron leading-tight">
            <span className="text-white">The Future of</span>
            <br />
            <span className="bg-gradient-to-r from-gray-400 via-gray-200 to-gray-400 bg-clip-text text-transparent">
              AI-Powered Referrals
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl lg:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
            Transform your business with the world's most advanced referral platform. Where anyone can earn by sharing, 
            businesses pay only for confirmed results, and customers get better deals through trusted connections.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-20">
            <button className="bg-gradient-to-r from-gray-600 to-gray-400 hover:from-gray-400 hover:to-white text-black px-10 py-4 rounded-full font-bold text-lg flex items-center justify-center gap-3 transition-all duration-300 shadow-lg hover:shadow-gray-400/30 transform hover:scale-105">
              Start Earning Now
              <ArrowRight className="w-5 h-5" />
            </button>

            <button className="border-2 border-gray-600 hover:border-gray-400 text-gray-300 hover:text-white hover:bg-gray-900/50 px-10 py-4 rounded-full font-bold text-lg transition-all duration-300">
              Watch Demo
            </button>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {[
              { number: '10M+', label: 'Referrals Processed', icon: Users },
              { number: '500%', label: 'Average ROI Increase', icon: TrendingUp },
              { number: '99.9%', label: 'Platform Uptime', icon: Shield },
            ].map((stat) => (
              <div
                key={stat.label}
                className="text-center p-8 rounded-2xl bg-gray-900/50 backdrop-blur-sm border border-gray-800 hover:border-gray-600 hover:bg-gray-900/70 transition-all duration-300 group"
              >
                <stat.icon className="w-8 h-8 mx-auto mb-4 text-gray-400 group-hover:text-gray-300 transition-colors duration-300" />
                <div className="text-3xl font-bold text-white font-orbitron mb-2">{stat.number}</div>
                <div className="text-gray-400">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-8 bg-gray-900/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 font-orbitron text-white">
              Revolutionary
              <br />
              <span className="bg-gradient-to-r from-gray-400 via-gray-200 to-gray-400 bg-clip-text text-transparent">
                Features
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Experience the next generation of referral marketing with AI-powered insights and seamless automation
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: 'AI-Powered Matching',
                description: 'Our advanced AI connects the right referrers with perfect opportunities, maximizing success rates and earnings.',
              },
              {
                icon: Users,
                title: 'Community-Driven Growth',
                description: 'Build a network of trusted referrers who become your most powerful marketing channel.',
              },
              {
                icon: TrendingUp,
                title: 'Performance Analytics',
                description: 'Real-time insights and predictive analytics help optimize campaigns for maximum ROI.',
              },
              {
                icon: Shield,
                title: 'Secure & Transparent',
                description: 'Blockchain-verified transactions ensure complete transparency and trust in every referral.',
              },
              {
                icon: DollarSign,
                title: 'Instant Payouts',
                description: 'Get paid instantly when referrals convert. No waiting periods, no delays.',
              },
              {
                icon: Star,
                title: 'Premium Support',
                description: '24/7 dedicated support with AI-assisted troubleshooting and human expertise.',
              }
            ].map((feature) => (
              <div
                key={feature.title}
                className="p-8 rounded-2xl bg-gray-900/50 backdrop-blur-sm border border-gray-800 hover:border-gray-600 hover:bg-gray-900/70 transition-all duration-300 group transform hover:scale-105"
              >
                <div className="mb-6">
                  <feature.icon className="w-12 h-12 text-gray-400 group-hover:text-gray-300 transition-colors duration-300" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-white group-hover:text-gray-100 transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 font-orbitron text-white">
              How It
              <span className="bg-gradient-to-r from-gray-400 via-gray-200 to-gray-400 bg-clip-text text-transparent"> Works</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Three simple steps to transform your business growth
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                icon: Target,
                title: 'Create Your Offer',
                description: 'Set up your referral campaign with AI-optimized rewards and targeting parameters.',
              },
              {
                step: '02',
                icon: Users,
                title: 'Share & Earn',
                description: 'Referrers share your offers through their networks and earn commissions on every conversion.',
              },
              {
                step: '03',
                icon: Award,
                title: 'Track Results',
                description: 'Monitor performance in real-time and scale successful campaigns automatically.',
              }
            ].map((step, index) => (
              <div key={step.title} className="text-center group">
                <div className="relative mb-8">
                  <div className="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-gray-600 to-gray-400 flex items-center justify-center shadow-lg group-hover:shadow-gray-400/30 transition-all duration-300">
                    <step.icon className="w-10 h-10 text-black" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-gray-800 border-2 border-gray-600 flex items-center justify-center">
                    <span className="text-sm font-bold text-gray-300">{step.step}</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4 text-white">{step.title}</h3>
                <p className="text-gray-400 leading-relaxed">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-8 bg-gray-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <div className="p-12 rounded-3xl bg-gray-900/70 backdrop-blur-sm border border-gray-800 shadow-2xl">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 font-orbitron text-white">
              Ready to Transform
              <br />
              <span className="bg-gradient-to-r from-gray-400 via-gray-200 to-gray-400 bg-clip-text text-transparent">
                Your Business?
              </span>
            </h2>
            
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of businesses already leveraging the power of AI-driven referrals. 
              Start your journey today and watch your growth accelerate.
            </p>
            
            <button className="bg-gradient-to-r from-gray-600 to-gray-400 hover:from-gray-400 hover:to-white text-black px-12 py-5 rounded-full font-bold text-xl flex items-center gap-4 mx-auto transition-all duration-300 shadow-lg hover:shadow-gray-400/30 transform hover:scale-105">
              Launch Your Platform
              <ArrowRight className="w-6 h-6" />
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-8 border-t border-gray-800">
        <div className="max-w-6xl mx-auto text-center">
          <div className="font-orbitron text-xl font-bold bg-gradient-to-r from-gray-400 to-white bg-clip-text text-transparent mb-4">
            ReferralAI
          </div>
          <p className="text-gray-400 mb-6">The future of referral marketing is here.</p>
          <div className="flex justify-center space-x-8">
            {['Privacy', 'Terms', 'Support', 'API'].map((item) => (
              <a key={item} href="#" className="text-gray-400 hover:text-gray-300 transition-colors duration-300">
                {item}
              </a>
            ))}
          </div>
        </div>
      </footer>
    </div>
  );
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800&display=swap');
@import "tailwindcss";

:root {
  --foreground: #f1f5f9;
  --background: #0f172a;
  --silver: #cbd5e1;
  --silver-light: #e2e8f0;
  --silver-dark: #94a3b8;
  --accent: #3b82f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'Orbitron', monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Smooth animations */
.smooth-animation {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Clean, simple styles */
.smooth-transition {
  transition: all 0.2s ease-in-out;
}

/* Utility classes */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.text-balance {
  text-wrap: balance;
}

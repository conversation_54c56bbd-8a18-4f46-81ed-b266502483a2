@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800&display=swap');
@import "tailwindcss";

:root {
  --foreground: #ffffff;
  --background: #000000;
  --silver: #c0c0c0;
  --silver-light: #e5e5e5;
  --silver-dark: #8a8a8a;
  --gray-900: #111111;
  --gray-800: #1f1f1f;
  --gray-700: #2d2d2d;
  --gray-600: #404040;
  --gray-500: #525252;
  --gray-400: #a3a3a3;
  --gray-300: #d4d4d4;
  --gray-200: #e5e5e5;
  --gray-100: #f5f5f5;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'Orbitron', monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #000000;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #525252;
}

/* Smooth animations */
.smooth-animation {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Silver gradient text */
.text-silver-gradient {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 50%, #c0c0c0 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Glow effects */
.glow-silver {
  box-shadow: 0 0 20px rgba(192, 192, 192, 0.3);
}

.glow-silver-strong {
  box-shadow: 0 0 40px rgba(192, 192, 192, 0.5);
}

/* Hover animations */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Utility classes */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.text-balance {
  text-wrap: balance;
}

/* Background patterns */
.bg-grid {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
}

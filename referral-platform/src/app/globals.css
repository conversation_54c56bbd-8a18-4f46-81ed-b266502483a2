@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800&display=swap');
@import "tailwindcss";

:root {
  --foreground: #f1f5f9;
  --background: #0f172a;
  --silver: #cbd5e1;
  --silver-light: #e2e8f0;
  --silver-dark: #94a3b8;
  --accent: #3b82f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'Orbitron', monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Smooth animations */
.smooth-animation {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Background */
.hero-bg {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  position: relative;
}

.hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Floating particles - simplified */
.particle {
  position: fixed;
  width: 2px;
  height: 2px;
  background: rgba(203, 213, 225, 0.3);
  border-radius: 50%;
  pointer-events: none;
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-30px) translateX(10px);
    opacity: 0.6;
  }
}

/* Text effects */
.text-shimmer {
  background: linear-gradient(90deg, #cbd5e1 0%, #f1f5f9 50%, #cbd5e1 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 4s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Button effects */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  border: 2px solid #475569;
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  border-color: #cbd5e1;
  background: rgba(30, 41, 59, 0.8);
  transform: translateY(-2px);
}

/* Card effects */
.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(203, 213, 225, 0.1);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(30, 41, 59, 0.6);
  border-color: rgba(203, 213, 225, 0.2);
  transform: translateY(-5px);
}

/* Utility classes */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.text-balance {
  text-wrap: balance;
}

/* Remove problematic animations */
.no-animation * {
  animation: none !important;
  transition: none !important;
}
